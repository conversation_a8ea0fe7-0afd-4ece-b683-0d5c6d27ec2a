require('dotenv').config();
const https = require('follow-redirects').https;
const fs = require('fs');

// Load API token from .env
const apiToken = process.env.TT_API_TOKEN;

if (!apiToken) {
  console.error("❌ API token missing. Please set TT_API_TOKEN in your .env file.");
  process.exit(1);
}

const authHeader = 'Basic ' + Buffer.from(':' + apiToken).toString('base64');

// Get today's date in YYYY-MM-DD format
const today = new Date().toISOString().split('T')[0];

// Query for events happening today
const queryParams = new URLSearchParams({
  limit: '200',
  status: 'published',
  'start.gte': today,
  'start.lt': new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]
});

const path = `/v1/events?${queryParams.toString()}`;
const fullUrl = `https://api.tickettailor.com${path}`;
console.log(`🔗 Requesting: ${fullUrl}`);

const options = {
  method: 'GET',
  hostname: 'api.tickettailor.com',
  path: path,
  headers: {
    'Accept': 'application/json',
    'Authorization': authHeader
  },
  maxRedirects: 20
};

const req = https.request(options, function (res) {
  let chunks = [];

  res.on('data', function (chunk) {
    chunks.push(chunk);
  });

  res.on('end', function () {
    const body = Buffer.concat(chunks).toString();

    try {
      const parsed = JSON.parse(body);

      if (!parsed.data || parsed.data.length === 0) {
        console.log("⚠️ No events found.");
        return;
      }

      // Filter events for today and collect all event details
      const todaysEvents = parsed.data.filter(event => {
        const eventDate = event.start?.date;
        return eventDate === today;
      });

      if (todaysEvents.length === 0) {
        console.log(`⚠️ No events found for today (${today}).`);
        return;
      }

      console.log(`\n✅ Found ${todaysEvents.length} event(s) for today (${today}):`);
      console.log(JSON.stringify(todaysEvents, null, 2));

      // Optionally write to file
      fs.writeFileSync('todaysEvents.json', JSON.stringify(todaysEvents, null, 2));
      console.log("\n💾 Output saved to todaysEvents.json");

    } catch (err) {
      console.error("❌ JSON parse error:", err.message);
    }
  });

  res.on('error', function (error) {
    console.error('❌ Request error:', error.message);
  });
});

req.end();
