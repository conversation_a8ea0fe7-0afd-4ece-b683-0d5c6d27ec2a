require('dotenv').config();
const https = require('follow-redirects').https;
const fs = require('fs');

// Load API token from .env
const apiToken = process.env.TT_API_TOKEN;

if (!apiToken) {
  console.error("❌ API token missing. Please set TT_API_TOKEN in your .env file.");
  process.exit(1);
}

const authHeader = 'Basic ' + Buffer.from(':' + apiToken).toString('base64');

// Get today's date in YYYY-MM-DD format
const today = new Date().toISOString().split('T')[0];

// Query with event name and filter for today onwards
const queryParams = new URLSearchParams({
  limit: '200',
  name: 'Very Mulberry U-Pick',
  status: 'published',
  'start.gte': today  // Only events starting from today onwards
});

const path = `/v1/events?${queryParams.toString()}`;
const fullUrl = `https://api.tickettailor.com${path}`;
console.log(`🔗 Requesting "Very Mulberry U-Pick" events from ${today} onwards: ${fullUrl}`);

const options = {
  method: 'GET',
  hostname: 'api.tickettailor.com',
  path: path,
  headers: {
    'Accept': 'application/json',
    'Authorization': authHeader
  },
  maxRedirects: 20
};

const req = https.request(options, function (res) {
  let chunks = [];

  res.on('data', function (chunk) {
    chunks.push(chunk);
  });

  res.on('end', function () {
    const body = Buffer.concat(chunks).toString();

    try {
      const parsed = JSON.parse(body);

      if (!parsed.data || parsed.data.length === 0) {
        console.log("⚠️ No events found.");
        return;
      }

      // Return all event details instead of just checkout URLs
      console.log(`\n✅ Found ${parsed.data.length} "Very Mulberry U-Pick" event(s) from ${today} onwards:`);
      console.log(JSON.stringify(parsed.data, null, 2));

      // Optionally write to file
      fs.writeFileSync('veryMulberryEvents.json', JSON.stringify(parsed.data, null, 2));
      console.log("\n💾 Output saved to veryMulberryEvents.json");

    } catch (err) {
      console.error("❌ JSON parse error:", err.message);
    }
  });

  res.on('error', function (error) {
    console.error('❌ Request error:', error.message);
  });
});

req.end();
